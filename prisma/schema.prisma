generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums for better type safety and data integrity
enum AccountType {
  oauth
  email
  credentials
}

enum WorkspaceRole {
  owner
  admin
  member
  viewer
}

enum WorkspaceSource {
  manual
  github
  gitlab
}

enum VCSProviderType {
  github
  gitlab
  bitbucket
}

enum PullRequestStatus {
  open
  closed
  merged
  draft
}

enum FileChangeStatus {
  added
  modified
  removed
  deleted
  renamed
}

enum ReviewStatus {
  pending
  in_progress
  completed
  failed
  cancelled
}

enum CommentType {
  comment
  suggestion
  question
  praise
  issue
}

enum CommentSeverity {
  info
  warning
  error
  critical
}

enum JobStatus {
  queued
  processing
  completed
  failed
  cancelled
  retrying
}

enum AIModel {
  gpt_4
  gpt_4_turbo
  gpt_3_5_turbo
  claude_3_opus
  claude_3_sonnet
  claude_3_haiku
  gemini_pro
  default
}

// NextAuth.js Models
model Account {
  id                String      @id @default(cuid())
  userId            String
  type              AccountType
  provider          String
  providerAccountId String
  refresh_token     String?     @db.Text
  access_token      String?     @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?     @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String            @id @default(cuid())
  name          String?
  email         String?           @unique
  emailVerified DateTime?
  image         String?
  accounts      Account[]
  sessions      Session[]
  workspaces    WorkspaceMember[]
  vcsInstallations VCSInstallation[]
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Application Models
model Workspace {
  id            String            @id @default(cuid())
  name          String
  slug          String            @unique
  description   String?
  source        WorkspaceSource   @default(manual)
  members       WorkspaceMember[]
  installations VCSInstallation[]
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt
}

model WorkspaceMember {
  id          String        @id @default(cuid())
  userId      String
  workspaceId String
  role        WorkspaceRole @default(member)
  user        User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  workspace   Workspace     @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  @@unique([userId, workspaceId])
}

// VCS Integration Models (GitHub, GitLab)
model VCSProvider {
  id            String            @id @default(cuid())
  type          VCSProviderType
  name          String
  installations VCSInstallation[]
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt
}

model VCSInstallation {
  id             String       @id @default(cuid())
  userId         String // User who made the installation
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  providerId     String
  provider       VCSProvider  @relation(fields: [providerId], references: [id], onDelete: Cascade)
  installationId String // GitHub App installation ID or GitLab integration ID
  accountId      String // GitHub/GitLab account ID
  accountLogin   String // GitHub/GitLab username or org name
  accessToken    String?      @db.Text // For GitLab access token (encrypted)
  tokenExpiresAt DateTime? // For token expiration tracking
  repositories   Repository[]
  workspaceId    String? // Optional link to workspace
  workspace      Workspace?   @relation(fields: [workspaceId], references: [id])
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  @@unique([userId, providerId])
  @@unique([providerId, installationId])
}

model Repository {
  id                String              @id @default(cuid())
  vcsInstallationId String
  vcsInstallation   VCSInstallation     @relation(fields: [vcsInstallationId], references: [id], onDelete: Cascade)
  externalId        String // GitHub/GitLab repo ID
  name              String // Repo name
  fullName          String // Full repo name (owner/name)
  private           Boolean // Is private repo
  defaultBranch     String              @default("main")
  pullRequests      PullRequest[]
  settings          RepositorySettings?
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@unique([vcsInstallationId, externalId])
}

model RepositorySettings {
  id             String     @id @default(cuid())
  repositoryId   String     @unique
  repository     Repository @relation(fields: [repositoryId], references: [id], onDelete: Cascade)
  autoReview     Boolean    @default(true)
  reviewPrivate  Boolean    @default(true)
  reviewTemplate String?    @db.Text
  excludePaths   String[] // Paths to exclude from reviews
  aiModel        AIModel    @default(default)
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt
}

model PullRequest {
  id           String            @id @default(cuid())
  repositoryId String
  repository   Repository        @relation(fields: [repositoryId], references: [id], onDelete: Cascade)
  externalId   String // PR/MR ID in GitHub/GitLab
  number       Int // PR/MR number
  title        String
  description  String?           @db.Text
  sourceBranch String // Source branch
  targetBranch String // Target branch
  status       PullRequestStatus @default(open)
  author       String // Author username
  authorId     String // Author ID
  url          String // URL to PR/MR
  reviews      Review[]
  files        PullRequestFile[]
  jobs         ReviewJob[]
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt

  @@unique([repositoryId, number])
}

model PullRequestFile {
  id             String           @id @default(cuid())
  pullRequestId  String
  pullRequest    PullRequest      @relation(fields: [pullRequestId], references: [id], onDelete: Cascade)
  path           String // File path
  status         FileChangeStatus
  reviewComments ReviewComment[]
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt

  @@unique([pullRequestId, path])
}

model Review {
  id             String          @id @default(cuid())
  pullRequestId  String
  pullRequest    PullRequest     @relation(fields: [pullRequestId], references: [id], onDelete: Cascade)
  externalId     String? // External review ID (if applicable)
  status         ReviewStatus    @default(pending)
  summary        String?         @db.Text // Overall summary
  comments       ReviewComment[]
  metrics        ReviewMetrics?
  processingTime Int? // Time taken to process in ms
  aiModel        AIModel? // AI model used
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
}

model ReviewComment {
  id         String           @id @default(cuid())
  reviewId   String
  review     Review           @relation(fields: [reviewId], references: [id], onDelete: Cascade)
  fileId     String?
  file       PullRequestFile? @relation(fields: [fileId], references: [id], onDelete: SetNull)
  externalId String? // External comment ID (if posted to GitHub/GitLab)
  body       String           @db.Text
  position   Int? // Line position
  startLine  Int? // For multi-line comments
  endLine    Int? // For multi-line comments
  suggestion String?          @db.Text // Code suggestion
  type       CommentType      @default(comment)
  severity   CommentSeverity?
  createdAt  DateTime         @default(now())
  updatedAt  DateTime         @updatedAt
}

// Analytics and Metrics
model ReviewMetrics {
  id               String   @id @default(cuid())
  reviewId         String   @unique
  review           Review   @relation(fields: [reviewId], references: [id], onDelete: Cascade)
  issuesFound      Int      @default(0)
  suggestionsGiven Int      @default(0)
  filesReviewed    Int      @default(0)
  linesReviewed    Int      @default(0)
  tokensUsed       Int      @default(0)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
}

// Job Queue for Processing
model ReviewJob {
  id            String      @id @default(cuid())
  pullRequestId String
  pullRequest   PullRequest @relation(fields: [pullRequestId], references: [id], onDelete: Cascade)
  status        JobStatus   @default(queued)
  priority      Int         @default(0)
  attempts      Int         @default(0)
  maxAttempts   Int         @default(3)
  error         String?     @db.Text
  processorId   String? // ID of worker processing this job
  startedAt     DateTime?
  completedAt   DateTime?
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  @@index([status, priority, createdAt])
}
