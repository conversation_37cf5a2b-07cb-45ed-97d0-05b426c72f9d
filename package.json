{"name": "platyfend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3001", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "seed:stripe": "node src/scripts/setup-stripe-products.js", "seed:stripe-ts": "ts-node src/scripts/setup-stripe-products.ts", "seed:billing": "node src/scripts/initialize-billing.js", "seed:billing-ts": "ts-node src/scripts/initialize-billing.ts", "fix:workspace": "node src/scripts/fix-workspace-plan.js", "trigger:dev": "npx trigger.dev@latest dev", "trigger:deploy": "npx trigger.dev@latest deploy", "trigger:init": "npx trigger.dev@latest init", "postinstall": "prisma generate", "prisma:studio": "prisma studio", "migrate:builtin-apps": "npx tsx src/scripts/migrate-builtin-apps.ts", "seed": "npx tsx src/scripts/seed.ts", "seed:apps": "npx tsx src/scripts/seed-apps.ts", "seed:instantly": "npx tsx src/scripts/seed-instantly-tools.ts", "seed:instantly:help": "echo 'Usage: npm run seed:instantly -- --workspace-id=<WORKSPACE_ID> --chatbot-id=<CHATBOT_ID> --bearer-token=<TOKEN>'", "local:stripe": "stripe listen --forward-to localhost:3001/api/webhooks/stripe", "ngrok": "ngrok http 3001"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/google": "^1.2.19", "@ai-sdk/openai": "^1.3.22", "@auth/prisma-adapter": "^2.9.1", "@aws-sdk/client-s3": "^3.828.0", "@aws-sdk/lib-storage": "^3.830.0", "@aws-sdk/s3-request-presigner": "^3.830.0", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.0.1", "@lottiefiles/dotlottie-react": "^0.14.0", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@mendable/firecrawl-js": "^1.25.1", "@next/mdx": "^15.3.5", "@next/third-parties": "^15.3.4", "@number-flow/react": "^0.5.10", "@octokit/rest": "^22.0.0", "@prisma/client": "^6.11.1", "@prisma/extension-accelerate": "^2.0.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@sentry/nextjs": "^9.33.0", "@stripe/stripe-js": "^7.3.1", "@tanstack/react-query": "^5.62.7", "@trigger.dev/build": "^3.3.17", "@trigger.dev/sdk": "^3.3.17", "@types/bcryptjs": "^3.0.0", "@types/multer": "^1.4.12", "@types/pdf-parse": "^1.1.5", "ai": "^4.3.16", "amqplib": "^0.10.8", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cobe": "^0.6.4", "color-bits": "^1.1.0", "dotenv": "^16.5.0", "dotted-map": "^2.2.3", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.19.1", "gray-matter": "^4.0.3", "input-otp": "^1.4.2", "lucide-react": "^0.511.0", "mammoth": "^1.9.0", "marked": "^15.0.12", "multer": "^2.0.0", "next": "15.3.2", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "octokit": "^5.0.3", "pdf2json": "^3.1.6", "react": "^19.0.0", "react-day-picker": "^9.8.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.3", "react-scan": "^0.3.4", "react-use-measure": "^2.1.7", "reading-time": "^1.5.0", "recharts": "^2.15.3", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "shiki": "^3.7.0", "sonner": "^2.0.3", "stripe": "^18.1.1", "styled-components": "^6.1.19", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.25.30"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.3.5", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/amqplib": "^0.10.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.3.2", "prisma": "^6.11.1", "tailwindcss": "^4", "ts-node": "^10.9.2", "tw-animate-css": "^1.3.0", "typescript": "^5"}}