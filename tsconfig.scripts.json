{"extends": "./tsconfig.json", "compilerOptions": {"module": "ESNext", "moduleResolution": "node", "target": "ES2020", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "outDir": "./dist"}, "include": ["src/scripts/**/*"], "exclude": ["node_modules", "dist"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}