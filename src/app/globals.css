@import "tailwindcss";

@font-face {
  font-family: 'Frutiger';
  src: url('/Frutiger_bold.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

:root {
  --background: 248 250 252; /* <PERSON> 50 */
  --foreground: 0 0 0; /* <PERSON> 800 */
  --card: 255 255 255; /* White */
  --card-foreground: 30 41 59; /* <PERSON> 800 */
  --popover: 255 255 255; /* White */
  --popover-foreground: 30 41 59; /* <PERSON> 800 */
  --primary: 0 97 123; /* Brand primary #00617b */
  --primary-foreground: 255 255 255;
  --secondary: 241 245 249; /* <PERSON> 100 */
  --secondary-foreground: 71 85 105; /* <PERSON> 600 */
  --muted: 241 245 249; /* <PERSON> 100 */
  --muted-foreground: 100 116 139; /* <PERSON> 500 */
  --accent: 241 245 249; /* <PERSON> 100 */
  --accent-foreground: 0 97 123; /* <PERSON> primary */
  --destructive: 239 68 68; /* Red 500 */
  --destructive-foreground: 255 255 255;
  --border: 0 0 0; /* <PERSON> 200 */
  --input: 226 232 240; /* <PERSON> 200 */
  --ring: 0 97 123; /* <PERSON> primary */
  --chart-1: oklch(0.8241 0.1251 84.4866);
  --chart-2: oklch(0.8006 0.1116 203.6044);
  --chart-3: oklch(0.4198 0.1693 266.7798);
  --chart-4: oklch(0.9214 0.0762 125.5777);
  --chart-5: oklch(0.9151 0.1032 116.1913);
  --sidebar: 255 255 255; /* White */
  --sidebar-foreground: 71 85 105; /* Gray 600 */
  --sidebar-primary: 0 97 123; /* Brand primary */
  --sidebar-primary-foreground: 255 255 255;
  --sidebar-accent: 241 245 249; /* Gray 100 */
  --sidebar-accent-foreground: 0 97 123; /* Brand primary */
  --sidebar-border: 226 232 240; /* Gray 200 */
  --sidebar-ring: 0 97 123; /* Brand primary */
  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
    "Liberation Mono", "Courier New", monospace;
  --radius: 0.625rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.1),
    0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.1),
    0 2px 4px -1px hsl(0 0% 0% / 0.1);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.1),
    0 4px 6px -1px hsl(0 0% 0% / 0.1);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.1),
    0 8px 10px -1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

/* Tailwind theme configuration moved to tailwind.config.ts */

@layer base {
  * {
    border-color: hsl(var(--border));
    outline-color: hsl(var(--ring) / 0.5);
  }

  /* Fix for 100vh issue on mobile Safari */
  html {
    height: -webkit-fill-available;
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    min-height: 100vh;
    min-height: -webkit-fill-available;
    min-height: 100dvh;
  }
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-scroll {
  animation: scroll 30s linear infinite;
}

.animate-scroll:hover {
  animation-play-state: paused;
}
