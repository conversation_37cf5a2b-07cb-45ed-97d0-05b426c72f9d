"use client";

import { useState } from "react";
import { Check, ChevronsUpDown, Plus, Building2, Lock } from "lucide-react";

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/src/components/ui/popover";


interface OrganizationSelectorProps {
  className?: string;
}

export function OrganizationSelector({}: OrganizationSelectorProps) {
  const [open, setOpen] = useState(false);

  return (
    <div className="w-full">
      
    </div>
  );
}
